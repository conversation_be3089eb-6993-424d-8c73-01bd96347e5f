"""
Marketing Agent - Marketing Strategy and Campaign Planning

The Marketing agent is responsible for:
- Campaign planning and execution
- Brand management and positioning
- Customer engagement strategies
- Market analysis and insights
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import base agent
from .base import BaseAgent

# Import orchestration components
from ..orchestration.state import Task, LangGraphState
from ..orchestration.memory import GlobalMemory, MemoryUnits
from ..orchestration.utils.shared_services import ContextService, TaskValidator
from ..orchestration.utils.error_handling import handle_orchestration_errors

# Import LLM components
from ..llm.base import LLMAdapter

logger = logging.getLogger(__name__)

class MarketingAgent(BaseAgent):
    """
    Marketing Agent - Specialized in marketing strategy and campaign planning.
    
    Responsibilities:
    - Campaign planning and execution
    - Brand management and positioning
    - Customer engagement strategies
    - Market analysis and insights
    """
    
    def __init__(
        self,
        llm_adapter: LLMAdapter,
        memory: Optional[GlobalMemory] = None,
    ):
        super().__init__(llm_adapter, memory)
        
        # Initialize shared services
        self.context_service = ContextService(llm_adapter)
        self.task_validator = TaskValidator()
        
        # Marketing identity and capabilities
        self.agent_name = "Marketing"
        self.capabilities = [
            "campaign_planning",
            "brand_management",
            "customer_engagement",
            "market_analysis",
            "content_strategy",
            "social_media_management",
            "performance_analytics",
            "audience_segmentation"
        ]
        
        # Domain-specific keywords for task matching
        self.domain_keywords = [
            "marketing", "campaign", "brand", "customer",
            "promotion", "advertising", "engagement", "market",
            "strategy", "content", "social", "digital",
            "audience", "segmentation", "analytics", "roi",
            "conversion", "acquisition", "retention", "loyalty"
        ]
        
        # Marketing-specific metrics and KPIs
        self.metrics = {
            "engagement": ["likes", "shares", "comments", "click-through"],
            "conversion": ["leads", "sales", "signups", "downloads"],
            "brand": ["awareness", "sentiment", "recall", "loyalty"],
            "performance": ["roi", "cpa", "cpc", "ctr"]
        }
        
        logger.info(f"[Marketing] Initialized with {len(self.capabilities)} capabilities")
    
    def build_system_prompt(self, task_description: str, _agent_name: str) -> str:
        """Build system prompt for marketing strategy tasks."""
        return (
            f"You are a marketing strategist with expertise in business marketing.\n"
            f"Your core capabilities include:\n"
            f"- Campaign planning and execution\n"
            f"- Brand management and positioning\n"
            f"- Customer engagement strategies\n"
            f"- Market analysis and insights\n"
            f"- Content strategy and social media management\n"
            f"- Performance analytics and optimization\n\n"
            f"Task: {task_description}\n\n"
            f"Use your marketing expertise to provide strategic recommendations. Focus on:\n"
            f"1. Clear campaign objectives and KPIs\n"
            f"2. Target audience analysis and segmentation\n"
            f"3. Channel strategy and content planning\n"
            f"4. Brand consistency and positioning\n"
            f"5. Performance measurement and optimization\n"
            f"6. Budget allocation and ROI tracking"
        )
    
    @handle_orchestration_errors("Marketing")
    async def run(self, task: Task, context: Optional[LangGraphState] = None) -> str:
        """Execute a marketing strategy task by always calling the LLM."""
        logger.info(f"[Marketing] Executing task: {task.description[:100]}...")
        try:
            # Get relevant historical context
            historical_context = await self._get_historical_context(task)
            # Get RAG context if available
            rag_context = await self.context_service.gather_context(task.description, context)
            # Build messages for LLM
            messages = [
                {"role": "system", "content": self.build_system_prompt(task.description, self.agent_name)}
            ]
            if historical_context:
                context_str = "\n".join(f"- {c.content}" for c in historical_context)
                messages.append({"role": "system", "content": f"Relevant historical context:\n{context_str}"})
            if rag_context:
                rag_str = "\n".join(f"- {s}" for s in rag_context.get("snippets", []))
                messages.append({"role": "system", "content": f"Relevant knowledge base context:\n{rag_str}"})
            # Get LLM response
            response = await self.llm_adapter.chat(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )
            # Store result in memory
            await self._store_analysis_result(task, response)
            return response
        except Exception as e:
            logger.exception(f"[Marketing] Error executing task: {e}")
            return f"[ERROR] {str(e)}"
    
    async def _get_historical_context(self, _task: Task) -> List[MemoryUnits]:
        """Get relevant historical context from memory."""
        if not self.memory:
            return []
            
        memory_filters = {
            "agent": self.agent_name,
            "any_tags": ["campaign", "strategy", "analysis", "performance"],
            "limit": 5
        }
        return self.memory.get_relevant_memory(memory_filters)
    
    async def _store_analysis_result(self, task: Task, result: str):
        """Store analysis result in memory."""
        if not self.memory:
            return
            
        memory_unit = MemoryUnits(
            content=f"Marketing strategy result for {task.description[:100]}...",
            agent=self.agent_name,
            task_id=task.task_id,
            current_step=task.current_step,
            tags=["strategy", "result", "marketing"],
            metadata={
                "task_type": task.task_type,
                "timestamp": datetime.now().isoformat(),
                "metrics": self._extract_marketing_metrics(result)
            }
        )
        self.memory.add_memory(memory_unit)
    
    def _extract_marketing_metrics(self, response: str) -> Dict[str, Any]:
        """Extract marketing-specific metrics from the response."""
        metrics = {}
        for category, kpis in self.metrics.items():
            metrics[category] = {
                kpi: self._find_metric_value(response, kpi)
                for kpi in kpis
            }
        return metrics
    
    def _find_metric_value(self, text: str, metric: str) -> Optional[float]:
        """Find a specific metric value in the text."""
        # This is a simple implementation - could be enhanced with better NLP
        try:
            # Look for patterns like "metric: value" or "metric value"
            import re
            pattern = f"{metric}[^0-9]*([0-9.]+)"
            match = re.search(pattern, text.lower())
            if match:
                return float(match.group(1))
        except Exception:
            pass
        return None 