"""
Finance Agent - Financial Analysis and Planning

The Finance agent is responsible for:
- Cost analysis and optimization
- Budget planning and forecasting
- ROI and financial metrics analysis
- Financial risk assessment
"""

import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime

# Import base agent
from .base import BaseAgent

# Import orchestration components
from ..orchestration.state import Task, LangGraphState
from ..orchestration.memory import GlobalMemory, MemoryUnits
from ..orchestration.utils.shared_services import ContextService, TaskValidator
from ..orchestration.utils.error_handling import handle_orchestration_errors

# Import LLM components
from ..llm.base import LLMAdapter

logger = logging.getLogger(__name__)

class FinanceAgent(BaseAgent):
    """
    Finance Agent - Specialized in financial analysis and planning.
    
    Responsibilities:
    - Cost analysis and optimization
    - Budget planning and forecasting
    - ROI and financial metrics analysis
    - Financial risk assessment
    """
    
    def __init__(
        self,
        llm_adapter: LLMAdapter,
        memory: Optional[GlobalMemory] = None,
    ):
        super().__init__(llm_adapter, memory)
        
        # Initialize shared services
        self.context_service = ContextService(llm_adapter)
        self.task_validator = TaskValidator()
        
        # Finance identity and capabilities
        self.agent_name = "Finance"
        self.capabilities = [
            "cost_analysis",
            "budget_planning",
            "roi_analysis",
            "financial_risk",
            "cash_flow_management",
            "investment_analysis",
            "financial_forecasting",
            "compliance_monitoring"
        ]
        
        # Domain-specific keywords for task matching
        self.domain_keywords = [
            "finance", "cost", "budget", "revenue", 
            "profit", "roi", "investment", "financial",
            "expense", "pricing", "forecast", "analysis",
            "cash_flow", "risk", "compliance", "audit",
            "tax", "accounting", "valuation", "metrics"
        ]
        
        # Finance-specific metrics and KPIs
        self.metrics = {
            "profitability": ["gross_margin", "net_margin", "roi", "roic"],
            "liquidity": ["current_ratio", "quick_ratio", "cash_ratio"],
            "efficiency": ["asset_turnover", "inventory_turnover", "receivables_days"],
            "growth": ["revenue_growth", "profit_growth", "customer_growth"],
            "risk": ["debt_ratio", "interest_coverage", "beta"]
        }
        
        logger.info(f"[Finance] Initialized with {len(self.capabilities)} capabilities")
    
    def build_system_prompt(self, task_description: str, _agent_name: str) -> str:
        """Build system prompt for financial analysis tasks."""
        return (
            f"You are a financial analyst with expertise in business finance.\n"
            f"Your core capabilities include:\n"
            f"- Cost analysis and optimization\n"
            f"- Budget planning and forecasting\n"
            f"- ROI and financial metrics analysis\n"
            f"- Financial risk assessment\n"
            f"- Cash flow management\n"
            f"- Investment analysis and valuation\n\n"
            f"Task: {task_description}\n\n"
            f"Use your financial expertise to provide detailed analysis. Focus on:\n"
            f"1. Clear financial metrics and KPIs\n"
            f"2. Data-driven recommendations\n"
            f"3. Risk assessment and mitigation strategies\n"
            f"4. Cost-benefit analysis\n"
            f"5. Cash flow implications\n"
            f"6. Compliance and regulatory considerations"
        )
    
    @handle_orchestration_errors("Finance")
    async def run(self, task: Task, context: Optional[LangGraphState] = None) -> str:
        """Execute a financial analysis task by always calling the LLM."""
        logger.info(f"[Finance] Executing task: {task.description[:100]}...")
        try:
            # Get relevant historical context
            historical_context = await self._get_historical_context(task)
            # Get RAG context if available
            rag_context = await self.context_service.gather_context(task.description, context)
            # Build messages for LLM
            messages = [
                {"role": "system", "content": self.build_system_prompt(task.description, self.agent_name)}
            ]
            if historical_context:
                context_str = "\n".join(f"- {c.content}" for c in historical_context)
                messages.append({"role": "system", "content": f"Relevant historical context:\n{context_str}"})
            if rag_context:
                rag_str = "\n".join(f"- {s}" for s in rag_context.get("snippets", []))
                messages.append({"role": "system", "content": f"Relevant knowledge base context:\n{rag_str}"})
            # Get LLM response
            response = await self.llm_adapter.chat(
                messages=messages,
                temperature=0.3,
                max_tokens=1000
            )
            # Store result in memory
            await self._store_analysis_result(task, response)
            return response
        except Exception as e:
            logger.exception(f"[Finance] Error executing task: {e}")
            return f"[ERROR] {str(e)}"
    
    async def _get_historical_context(self, _task: Task) -> List[MemoryUnits]:
        """Get relevant historical context from memory."""
        if not self.memory:
            return []
            
        memory_filters = {
            "agent": self.agent_name,
            "any_tags": ["analysis", "forecast", "risk", "budget"],
            "limit": 5
        }
        return self.memory.get_relevant_memory(memory_filters)
    
    async def _store_analysis_result(self, task: Task, result: str):
        """Store analysis result in memory."""
        if not self.memory:
            return
            
        memory_unit = MemoryUnits(
            content=f"Financial analysis result for {task.description[:100]}...",
            agent=self.agent_name,
            task_id=task.task_id,
            current_step=task.current_step,
            tags=["analysis", "result", "finance"],
            metadata={
                "task_type": task.task_type,
                "timestamp": datetime.now().isoformat(),
                "metrics": self._extract_financial_metrics(result)
            }
        )
        self.memory.add_memory(memory_unit)
    
    def _extract_financial_metrics(self, response: str) -> Dict[str, Any]:
        """Extract financial metrics from the response."""
        metrics = {}
        for category, kpis in self.metrics.items():
            metrics[category] = {
                kpi: self._find_metric_value(response, kpi)
                for kpi in kpis
            }
        return metrics
    
    def _find_metric_value(self, text: str, metric: str) -> Optional[float]:
        """Find a specific metric value in the text."""
        # This is a simple implementation - could be enhanced with better NLP
        try:
            # Look for patterns like "metric: value" or "metric value"
            pattern = f"{metric}[^0-9]*([0-9.]+)"
            match = re.search(pattern, text.lower())
            if match:
                return float(match.group(1))
        except Exception:
            pass
        return None 