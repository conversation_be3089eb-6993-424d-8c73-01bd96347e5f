"""
Shared services for orchestration components.

This module provides centralized implementations of common functionality
used across different orchestration components.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

from .state import Task, TaskType, TaskStatus
from ..rag.llm import RAGLLMAdapter
from ..rag import KnowledgeBaseService
from ..app.config import get_settings

logger = logging.getLogger(__name__)

class ContextService:
    """Service for gathering and managing context across the system."""
    
    def __init__(
        self,
        llm_adapter: Optional[RAGLLMAdapter] = None,
        knowledge_base: Optional[KnowledgeBaseService] = None
    ):
        self.llm_adapter = llm_adapter
        self.knowledge_base = knowledge_base
        self.settings = get_settings()
    
    async def gather_context(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        include_memory: bool = False,
        memory_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gather context from all available sources.

        Args:
            query: The query to gather context for
            context: Additional context to consider
            limit: Maximum number of results to retrieve
            include_memory: Whether to include memory context
            memory_filters: Filters for memory retrieval

        Returns:
            Dictionary containing gathered context
        """
        if context is None:
            context = {}

        gathered_context = {
            "rag_results": [],
            "memory_context": [],
            "domain_insights": [],
            "complexity_indicators": [],
            "suggested_agents": [],
            "search_metadata": {},
            "timestamp": datetime.now().isoformat()
        }

        # Get RAG results if available
        if self.knowledge_base:
            try:
                rag_results = await self.knowledge_base.search(
                    query=query,
                    limit=limit
                )
                gathered_context["rag_results"] = rag_results
                logger.debug(f"Retrieved {len(rag_results)} RAG results")
            except Exception as e:
                logger.warning(f"Error querying knowledge base: {e}")

        # Get memory context if requested and available
        if include_memory and hasattr(self, 'memory') and self.memory:
            try:
                if memory_filters is None:
                    memory_filters = {"limit": limit}
                memory_results = self.memory.get_relevant_memory(memory_filters)
                gathered_context["memory_context"] = [
                    {
                        "content": m.content,
                        "timestamp": m.timestamp.isoformat(),
                        "tags": m.tags
                    }
                    for m in memory_results
                ]
                logger.debug(f"Retrieved {len(memory_results)} memory items")
            except Exception as e:
                logger.warning(f"Error retrieving memory context: {e}")

        return gathered_context

class TaskValidator:
    """Service for validating task plans and individual tasks."""
    
    @staticmethod
    def validate_task_plan(tasks: List[Task]) -> Tuple[bool, Optional[str]]:
        """
        Validate a task plan for logical consistency.
        
        Args:
            tasks: List of tasks to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not tasks or len(tasks) > 20:  # Reasonable limits for validation
            return False, "Invalid number of tasks"
            
        # Check for circular dependencies using topological sort
        task_ids = {task.task_id for task in tasks}
        
        # Verify all dependencies reference valid tasks
        for task in tasks:
            if not task.dependencies.issubset(task_ids):
                return False, f"Task {task.task_id} has invalid dependencies"
        
        # Check for cycles
        visited = set()
        rec_stack = set()
        
        def has_cycle(task_id: str) -> bool:
            if task_id in rec_stack:
                return True
            if task_id in visited:
                return False
                
            visited.add(task_id)
            rec_stack.add(task_id)
            
            task = next((t for t in tasks if t.task_id == task_id), None)
            if task:
                for dep_id in task.dependencies:
                    if has_cycle(dep_id):
                        return True
                        
            rec_stack.remove(task_id)
            return False
        
        # Check each task for cycles
        for task in tasks:
            if task.task_id not in visited:
                if has_cycle(task.task_id):
                    return False, "Circular dependency detected in task plan"
        
        return True, None
    
    @staticmethod
    def validate_task(task: Task) -> Tuple[bool, Optional[str]]:
        """
        Validate an individual task.
        
        Args:
            task: Task to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not task.description or not task.description.strip():
            return False, "Task description cannot be empty"
            
        if not isinstance(task.task_type, TaskType):
            return False, f"Invalid task type: {task.task_type}"
            
        if not isinstance(task.status, TaskStatus):
            return False, f"Invalid task status: {task.status}"
            
        return True, None

class ServiceInitializer:
    """Service for initializing and managing shared services."""
    
    @staticmethod
    async def initialize_services(
        llm_adapter: Optional[RAGLLMAdapter] = None,
        knowledge_base: Optional[KnowledgeBaseService] = None,
        planning_temperature: float = 0.3
    ) -> Tuple[RAGLLMAdapter, KnowledgeBaseService]:
        """
        Initialize required services.
        
        Args:
            llm_adapter: Optional existing LLM adapter
            knowledge_base: Optional existing knowledge base
            planning_temperature: Temperature for planning operations
            
        Returns:
            Tuple of (LLMAdapter, KnowledgeBaseService)
        """
        settings = get_settings()
        
        try:
            if llm_adapter is None:
                from ..rag.llm import get_rag_llm_adapter
                llm_adapter = get_rag_llm_adapter()
                
            if knowledge_base is None:
                from ..rag import initialize_knowledge_base_service
                knowledge_base = await initialize_knowledge_base_service()
                
            return llm_adapter, knowledge_base
            
        except Exception as e:
            logger.error(f"Error initializing services: {e}")
            raise 